'use client';

import React, { useEffect, useState } from 'react';
import { FaBitcoin, FaPaypal } from 'react-icons/fa';
import CryptomusModal from '@/components/CryptomusModal';

type PaymentMethod = {
  payment_method_id: number;
  payment_method_name: string;
  original_price: number;
  discount_percent: number;
  discounted_price: number;
  savings: number;
  currency: string;
};

type USDTConversion = {
  originalAmount: number;
  convertedAmount: number;
  formattedAmount: string;
  exchangeRate: number;
};

type PaymentButtonsProps = {
  packageId: number;
  durationId: number;
  packageName: string;
  onPayPalClick?: (paymentMethod: PaymentMethod) => void;
  onCryptomusClick?: (paymentMethod: PaymentMethod) => void;
  className?: string;
};

const PaymentButtons: React.FC<PaymentButtonsProps> = ({
  packageId,
  durationId,
  packageName,
  onPayPalClick,
  onCryptomusClick,
  className = '',
}) => {
  const [paymentMethods, setPaymentMethods] = useState<PaymentMethod[]>([]);
  const [usdtConversions, setUsdtConversions] = useState<Map<number, USDTConversion>>(new Map());
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [showCryptomusModal, setShowCryptomusModal] = useState(false);
  const [selectedCryptomusMethod, setSelectedCryptomusMethod] = useState<PaymentMethod | null>(null);

  const fetchPaymentMethods = async () => {
    try {
      setLoading(true);
      setError(null);

      const response = await fetch(
        `/api/public/packages/pricing/${packageId}/${durationId}?currency=USD`,
      );

      if (!response.ok) {
        throw new Error('Failed to fetch payment methods');
      }

      const data = await response.json();
      setPaymentMethods(data.pricing_options || []);

      // Fetch USDT conversions for each payment method
      const conversions = new Map<number, USDTConversion>();

      for (const method of data.pricing_options || []) {
        try {
          const usdtResponse = await fetch(
            `/api/currency/convert-to-usdt?amount=${method.discounted_price}`,
          );

          if (usdtResponse.ok) {
            const usdtData = await usdtResponse.json();
            conversions.set(method.payment_method_id, usdtData.data);
          }
        } catch (usdtError) {
          console.warn(`Failed to get USDT conversion for payment method ${method.payment_method_id}:`, usdtError);
        }
      }

      setUsdtConversions(conversions);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'An error occurred');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchPaymentMethods();
  }, [packageId, durationId]);

  const handlePayPalClick = (paymentMethod: PaymentMethod) => {
    if (onPayPalClick) {
      onPayPalClick(paymentMethod);
    }
  };

  const handleCryptomusClick = (paymentMethod: PaymentMethod) => {
    setSelectedCryptomusMethod(paymentMethod);
    setShowCryptomusModal(true);

    if (onCryptomusClick) {
      onCryptomusClick(paymentMethod);
    }
  };

  const formatPrice = (amount: number, currency: string = 'USD') => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency,
    }).format(amount);
  };

  const getPaymentMethodIcon = (methodName: string) => {
    const name = methodName.toLowerCase();
    if (name.includes('paypal')) {
      return <FaPaypal className="mr-2 text-blue-600" />;
    }
    if (name.includes('cryptomus') || name.includes('crypto')) {
      return <FaBitcoin className="mr-2 text-orange-500" />;
    }
    return null;
  };

  if (loading) {
    return (
      <div className={`space-y-3 ${className}`}>
        <div className="h-12 animate-pulse rounded-lg bg-gray-200"></div>
        <div className="h-12 animate-pulse rounded-lg bg-gray-200"></div>
      </div>
    );
  }

  if (error) {
    return (
      <div className={`text-center text-red-600 ${className}`}>
        <p>
          Error loading payment options:
          {error}
        </p>
        <button
          onClick={fetchPaymentMethods}
          className="mt-2 text-blue-600 underline hover:text-blue-800"
        >
          Try again
        </button>
      </div>
    );
  }

  if (paymentMethods.length === 0) {
    return (
      <div className={`text-center text-gray-600 ${className}`}>
        <p>No payment methods available</p>
      </div>
    );
  }

  return (
    <div className={`space-y-4 ${className}`}>
      {paymentMethods.map((method) => {
        const usdtConversion = usdtConversions.get(method.payment_method_id);
        const isPayPal = method.payment_method_name.toLowerCase().includes('paypal');
        const isCryptomus = method.payment_method_name.toLowerCase().includes('cryptomus');

        return (
          <div
            key={method.payment_method_id}
            className="rounded-xl border-2 border-gray-200 bg-gradient-to-br from-white to-gray-50 p-5 shadow-lg transition-all duration-300 hover:scale-[1.02] hover:border-blue-300 hover:shadow-xl"
          >
            {/* Payment Method Header */}
            <div className="mb-3 flex items-center justify-between">
              <div className="flex items-center">
                {getPaymentMethodIcon(method.payment_method_name)}
                <span className="font-medium capitalize text-gray-900">
                  {method.payment_method_name}
                </span>
              </div>

              {method.discount_percent > 0 && (
                <span className="animate-pulse rounded-full bg-gradient-to-r from-green-500 to-emerald-500 px-3 py-1 text-sm font-bold text-white shadow-md">
                  🎉
                  {' '}
                  {method.discount_percent}
                  % OFF
                </span>
              )}
            </div>

            {/* Pricing Information */}
            <div className="mb-6 space-y-3 rounded-lg bg-gradient-to-r from-gray-50 to-blue-50 p-4">
              {method.discount_percent > 0 && (
                <div className="flex justify-between text-sm">
                  <span className="text-gray-600">💰 Original Price:</span>
                  <span className="font-medium text-gray-500 line-through">
                    {formatPrice(method.original_price)}
                  </span>
                </div>
              )}

              <div className="flex justify-between border-b border-gray-200 pb-2">
                <span className="font-semibold text-gray-900">💵 Final Price (USD):</span>
                <span className="text-xl font-bold text-green-600">
                  {formatPrice(method.discounted_price)}
                </span>
              </div>

              {usdtConversion && (
                <div className="flex justify-between">
                  <span className="font-semibold text-gray-900">🪙 Final Price (USDT):</span>
                  <span className="text-lg font-bold text-orange-600">
                    {usdtConversion.formattedAmount}
                  </span>
                </div>
              )}

              {method.savings > 0 && (
                <div className="flex justify-between rounded-md bg-green-100 px-3 py-2">
                  <span className="font-medium text-green-700">🎯 You Save:</span>
                  <span className="text-lg font-bold text-green-700">
                    {formatPrice(method.savings)}
                  </span>
                </div>
              )}
            </div>

            {/* Payment Button */}
            {isPayPal && (
              <button
                type="button"
                onClick={() => handlePayPalClick(method)}
                className="flex w-full items-center justify-center rounded-xl bg-gradient-to-r from-blue-600 to-blue-700 px-6 py-4 font-semibold text-white shadow-lg transition-all duration-300 hover:scale-105 hover:from-blue-700 hover:to-blue-800 hover:shadow-xl active:scale-95"
              >
                <FaPaypal className="mr-3 text-xl" />
                <span className="text-lg">Pay with PayPal</span>
              </button>
            )}

            {isCryptomus && (
              <button
                type="button"
                onClick={() => handleCryptomusClick(method)}
                className="flex w-full items-center justify-center rounded-xl bg-gradient-to-r from-orange-500 to-yellow-500 px-6 py-4 font-semibold text-white shadow-lg transition-all duration-300 hover:scale-105 hover:from-orange-600 hover:to-yellow-600 hover:shadow-xl active:scale-95"
              >
                <FaBitcoin className="mr-3 text-xl" />
                <span className="text-lg">Pay with Crypto</span>
              </button>
            )}
          </div>
        );
      })}

      {/* Cryptomus Modal */}
      {showCryptomusModal && selectedCryptomusMethod && (
        <CryptomusModal
          isOpen={showCryptomusModal}
          onClose={() => setShowCryptomusModal(false)}
          productName={packageName}
          discountPercent={selectedCryptomusMethod.discount_percent}
          originalPrice={selectedCryptomusMethod.original_price}
          discountedPrice={selectedCryptomusMethod.discounted_price}
        />
      )}
    </div>
  );
};

export default PaymentButtons;
