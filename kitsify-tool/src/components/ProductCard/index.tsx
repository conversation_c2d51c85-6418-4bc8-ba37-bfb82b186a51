import type { Product, ProductDuration } from '@/services/products';
import { useTranslations } from 'next-intl';
import Image from 'next/image';
import React, { useEffect, useState } from 'react';
import CryptomusModal from '../CryptomusModal';
import ProductDetailModal from '../ProductDetailModal';

type ProductCardProps = {
  product: Product;
  onAddToCart: (product: Product, quantity: number, selectedDuration: ProductDuration) => void;
};

const ProductCard: React.FC<ProductCardProps> = ({ product, onAddToCart }) => {
  const t = useTranslations('Shop');
  const [quantity, setQuantity] = useState(1);
  const [selectedDuration, setSelectedDuration] = useState<ProductDuration | null>(null);
  const [showDetailModal, setShowDetailModal] = useState(false);
  const [showCryptomusModal, setShowCryptomusModal] = useState(false);
  const {
    name,
    image_url,
    features: _features,
    durations = [],
    is_hot = false,
  } = product;

  // Chọn duration mặc định (thường là gói ngắn nhất)
  useEffect(() => {
    if (durations && durations.length > 0) {
      // Sắp xếp theo thời gian tăng dần và chọn gói đầu tiên
      const sortedDurations = [...durations].sort((a, b) => a.duration_days - b.duration_days);
      setSelectedDuration(sortedDurations[0] || null);
    }
  }, [durations]);

  if (!selectedDuration) {
    return null;
  }

  const isOutOfStock = selectedDuration.quantity <= 0;
  const hasDiscount = selectedDuration.discount_price > 0
    && selectedDuration.discount_price < selectedDuration.original_price;
  const displayPrice = hasDiscount
    ? Number(selectedDuration.discount_price)
    : Number(selectedDuration.original_price);
  const discountPercentage = hasDiscount
    ? Math.round(((Number(selectedDuration.original_price) - Number(selectedDuration.discount_price))
      / Number(selectedDuration.original_price)) * 100)
    : selectedDuration.discount_percent || 0;

  const handleIncrement = () => {
    if (quantity < selectedDuration.quantity) {
      setQuantity(prev => prev + 1);
    }
  };

  const handleDecrement = () => {
    if (quantity > 1) {
      setQuantity(prev => prev - 1);
    }
  };

  const handleAddToCart = () => {
    if (!isOutOfStock && selectedDuration) {
      onAddToCart(product, quantity, selectedDuration);
      setQuantity(1);
    }
  };

  const handleDurationChange = (durationId: number) => {
    const duration = durations.find(d => d.id === durationId);
    if (duration) {
      setSelectedDuration(duration);
      setQuantity(1);
    }
  };

  const handleViewDetail = () => {
    setShowDetailModal(true);
  };

  const handleCloseModal = () => {
    setShowDetailModal(false);
  };

  const handlePayPalPayment = () => {
    // PayPal payment with payment_method_id = 1 (hardcoded as requested)
    if (!isOutOfStock && selectedDuration) {
      onAddToCart(product, quantity, selectedDuration);
      setQuantity(1);
    }
  };

  const handleCryptomusPayment = () => {
    // Show Cryptomus modal
    setShowCryptomusModal(true);
  };

  const handleCloseCryptomusModal = () => {
    setShowCryptomusModal(false);
  };

  // Get cryptomus discount if available
  const getCryptomusDiscount = () => {
    const cryptomusDiscount = product.productDiscounts?.find(
      discount => discount.paymentMethod.name.toLowerCase() === 'cryptomus',
    );
    return cryptomusDiscount?.discount_percent || 0;
  };

  const cryptomusDiscountPercent = getCryptomusDiscount();
  const hasCryptomusDiscount = cryptomusDiscountPercent > 0;

  const cryptomusPrice = hasCryptomusDiscount
    ? displayPrice * (1 - cryptomusDiscountPercent / 100)
    : displayPrice;

  return (
    <>
      <div className="flex size-full flex-col overflow-hidden rounded-lg border border-gray-200 bg-white shadow-md transition-all duration-300 hover:shadow-lg">
        <div className="relative h-48 w-full shrink-0">
          {image_url
            ? (
                <Image
                  src={image_url}
                  alt={name}
                  fill
                  className="object-cover"
                />
              )
            : (
                <div className="flex size-full items-center justify-center bg-gray-200">
                  <span className="text-gray-400">No image</span>
                </div>
              )}
          {isOutOfStock && (
            <div className="absolute right-0 top-0 m-2 rounded bg-red-500 px-2 py-1 text-xs font-medium text-white">
              {t('soldOut') || 'Sold Out'}
            </div>
          )}
          {hasDiscount && (
            <div className="absolute left-0 top-0 m-2 rounded bg-green-500 px-2 py-1 text-xs font-medium text-white">
              -
              {discountPercentage}
              %
            </div>
          )}
          {is_hot && (
            <div className="absolute right-0 top-0 m-2 flex items-center rounded-full bg-gradient-to-r from-red-500 to-orange-500 px-3 py-1.5 shadow-lg">
              <svg
                className="mr-1 size-3 text-white"
                fill="currentColor"
                viewBox="0 0 24 24"
                xmlns="http://www.w3.org/2000/svg"
              >
                <path d="M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z" />
              </svg>
              <span className="text-xs font-bold text-white">HOT</span>
            </div>
          )}
        </div>

        <div className="flex flex-1 flex-col p-4">
          {/* Header Section - Fixed height */}
          <div className="mb-3 shrink-0">
            <h3 className="mb-2 text-lg font-semibold text-gray-900">{name}</h3>
          </div>

          {/* Content Section - Flexible height */}
          <div className="flex-1">
            {/* Duration Selection - Danh sách thay vì dropdown */}
            {durations.length > 1 && (
              <div className="mb-4">
                <label className="mb-2 block text-sm font-medium text-gray-700">
                  {t('duration') || 'Thời hạn'}
                  :
                </label>
                <div className="space-y-2">
                  {durations.map((duration) => {
                    const isSelected = selectedDuration?.id === duration.id;
                    const durationPrice = Number(duration.discount_price > 0 ? duration.discount_price : duration.original_price);
                    const durationHasDiscount = duration.discount_price > 0 && duration.discount_price < duration.original_price;

                    return (
                      <button
                        key={duration.id}
                        type="button"
                        onClick={() => handleDurationChange(duration.id)}
                        className={`w-full rounded-md border p-3 text-left text-sm transition-all duration-200 ${
                          isSelected
                            ? 'border-blue-500 bg-blue-50 ring-1 ring-blue-500'
                            : 'border-gray-200 bg-white hover:border-gray-300 hover:bg-gray-50'
                        }`}
                      >
                        <div className="flex items-center justify-between">
                          <div className="flex items-center space-x-2">
                            <div className={`size-4 rounded-full border-2 ${
                              isSelected ? 'border-blue-500 bg-blue-500' : 'border-gray-300'
                            }`}
                            >
                              {isSelected && (
                                <div className="size-full scale-[0.4] rounded-full bg-white" />
                              )}
                            </div>
                            <span className="font-medium text-gray-900">
                              {duration.duration_days}
                              {' '}
                              {t('days') || 'ngày'}
                            </span>
                          </div>
                          <div className="flex items-center space-x-2">
                            <span className={`font-bold ${durationHasDiscount ? 'text-red-600' : 'text-gray-900'}`}>
                              $
                              {durationPrice.toFixed(2)}
                            </span>
                            {durationHasDiscount && (
                              <span className="text-xs text-gray-500 line-through">
                                $
                                {Number(duration.original_price).toFixed(2)}
                              </span>
                            )}
                          </div>
                        </div>
                        {duration.quantity <= 5 && duration.quantity > 0 && (
                          <div className="mt-1 text-xs text-orange-600">
                            {t('onlyLeft') || 'Chỉ còn'}
                            {' '}
                            {duration.quantity}
                            {' '}
                            {t('remaining') || 'sản phẩm'}
                          </div>
                        )}
                      </button>
                    );
                  })}
                </div>
              </div>
            )}

            {/* Price Display for single duration */}
            {durations.length === 1 && (
              <div className="mb-3">
                <div className="flex items-center gap-2">
                  <span className={`text-lg font-bold ${hasDiscount ? 'text-red-600' : 'text-gray-900'}`}>
                    $
                    {displayPrice.toFixed(2)}
                  </span>
                  {hasDiscount && (
                    <span className="text-sm text-gray-500 line-through">
                      $
                      {Number(selectedDuration.original_price).toFixed(2)}
                    </span>
                  )}
                </div>
                <div className="text-sm text-gray-600">
                  {selectedDuration.duration_days}
                  {' '}
                  {t('days') || 'ngày'}
                </div>
              </div>
            )}
          </div>

          {/* Footer Section - Fixed at bottom */}
          <div className="mt-auto shrink-0 space-y-4">
            {/* Quantity selector */}
            <div className="flex items-center justify-between">
              <span className="text-sm font-medium text-gray-700">
                {t('quantity') || 'Số lượng'}
                :
              </span>
              <div className="flex items-center">
                <button
                  type="button"
                  onClick={handleDecrement}
                  disabled={quantity <= 1 || isOutOfStock}
                  className={`flex size-8 items-center justify-center rounded-l border border-gray-300 ${
                    quantity <= 1 || isOutOfStock ? 'cursor-not-allowed bg-gray-100 text-gray-400' : 'bg-gray-50 hover:bg-gray-100'
                  }`}
                  aria-label="Decrease quantity"
                >
                  -
                </button>
                <div className="flex h-8 w-10 items-center justify-center border-y border-gray-300 bg-white text-center text-sm">
                  {quantity}
                </div>
                <button
                  type="button"
                  onClick={handleIncrement}
                  disabled={quantity >= selectedDuration.quantity || isOutOfStock}
                  className={`flex size-8 items-center justify-center rounded-r border border-gray-300 ${
                    quantity >= selectedDuration.quantity || isOutOfStock ? 'cursor-not-allowed bg-gray-100 text-gray-400' : 'bg-gray-50 hover:bg-gray-100'
                  }`}
                  aria-label="Increase quantity"
                >
                  +
                </button>
              </div>
            </div>

            {/* Add to Cart Button */}
            <div className="space-y-2">
              {/* PayPal Button */}
              <button
                type="button"
                onClick={handlePayPalPayment}
                disabled={isOutOfStock}
                className={`w-full rounded-lg px-4 py-3 font-medium text-white transition-all duration-200${
                  isOutOfStock
                    ? 'cursor-not-allowed bg-gray-400'
                    : 'bg-gradient-to-r from-blue-600 to-blue-700 hover:-translate-y-0.5 hover:from-blue-700 hover:to-blue-800 hover:shadow-lg active:scale-95'
                }`}
              >
                <div className="flex items-center justify-center space-x-2">
                  <span>💳</span>
                  <span>{isOutOfStock ? (t('outOfStock') || 'Hết hàng') : `PayPal - ${t('addToCart') || 'Thêm vào giỏ'}`}</span>
                </div>
              </button>

              {/* Cryptomus Button (only show if discount available) */}
              {hasCryptomusDiscount && (
                <button
                  type="button"
                  onClick={handleCryptomusPayment}
                  disabled={isOutOfStock}
                  className={`relative w-full overflow-hidden rounded-lg px-4 py-3 font-medium transition-all duration-200${
                    isOutOfStock
                      ? 'cursor-not-allowed bg-gray-400 text-white'
                      : 'bg-gradient-to-r from-orange-500 to-yellow-500 text-white hover:-translate-y-0.5 hover:from-orange-600 hover:to-yellow-600 hover:shadow-lg active:scale-95'
                  }`}
                >
                  {/* Discount Badge */}
                  <div className="absolute -right-1 -top-1 animate-pulse rounded-full bg-red-500 px-2 py-1 text-xs font-bold text-white">
                    -
                    {cryptomusDiscountPercent}
                    %
                  </div>

                  {isOutOfStock
                    ? (t('outOfStock') || 'Hết hàng')
                    : (
                        <div className="flex items-center justify-between">
                          <div className="flex items-center space-x-2">
                            <span>🪙</span>
                            <span>Crypto Payment</span>
                          </div>
                          <div className="text-right">
                            <div className="text-xs line-through opacity-75">
                              $
                              {displayPrice.toFixed(2)}
                            </div>
                            <div className="text-lg font-bold">
                              $
                              {cryptomusPrice.toFixed(2)}
                            </div>
                          </div>
                        </div>
                      )}
                </button>
              )}
            </div>

            {/* View Detail Button */}
            <button
              type="button"
              onClick={handleViewDetail}
              className="w-full rounded-lg border border-blue-600 bg-white px-4 py-3 font-medium text-blue-600 transition-all duration-200 hover:-translate-y-0.5 hover:bg-blue-50 hover:shadow-md active:scale-95"
            >
              {t('viewMore')}
            </button>
          </div>
        </div>
      </div>

      {/* Product Detail Modal */}
      <ProductDetailModal
        product={product}
        isOpen={showDetailModal}
        onClose={handleCloseModal}
        selectedDuration={selectedDuration}
        quantity={quantity}
        onDurationChange={handleDurationChange}
        onQuantityChange={setQuantity}
        onAddToCart={handleAddToCart}
      />

      {/* Cryptomus Modal */}
      <CryptomusModal
        isOpen={showCryptomusModal}
        onClose={handleCloseCryptomusModal}
        productName={name}
        discountPercent={cryptomusDiscountPercent}
        originalPrice={displayPrice}
        discountedPrice={cryptomusPrice}
      />
    </>
  );
};

export default ProductCard;
